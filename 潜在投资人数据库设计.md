目的
要将这两张 Excel 表格存入数据库，核心是通过 “行业” 相关字段建立关联，同时保证每张表的主键唯一。
表一：围绕证券的基础信息和行业分类设计，
表二：则聚焦基金的行业配置数据，
以下是具体的数据结构设计方案：
表一：证券（股票）信息表（可命名为tonghuashun_stock_industry_info）
该表主要存储证券（股票）的基本信息及所属行业分类，需保证证券（股票）代码的唯一性。
字段名
数据类型
说明
约束
security_code
VARCHAR(20)
证券代码（如股票代码）
主键，非空
security_name
VARCHAR(100)
证券简称
非空
company_full_name
VARCHAR(200)
公司全称
非空
industry_category
VARCHAR(100)
所属新证监会行业（门类行业）
非空
industry_big_category
VARCHAR(100)
所属新证监会行业（大类行业）
可空
industry_detail
VARCHAR(255)
所属新证监会行业（全部明细）
可空
update_time
TIMESTAMP
表格更新时间
非空
示例：
[图片]
设计说明：
  - 证券（股票）代码作为主键，确保每只证券在表中唯一标识。
  - 行业相关字段保留原 Excel 中的层级关系，便于后续按不同行业级别统计分析。
  - 允许行业字段为空，避免因部分证券行业信息缺失导致数据无法入库。
表结构设计
/**
 * 证券（股票）信息表
 * @description 存储证券基本信息及行业分类，通过证券代码唯一标识
 */
model TonghuashunStockIndustryInfo {
  securityCode       String  @id @map("security_code") // 证券代码（如股票代码）
  securityName       String  @map("security_name") // 证券简称
  companyFullName    String  @map("company_full_name") // 公司全称
  industryCategory   String  @map("industry_category") // 所属新证监会行业（门类行业）
  industryBigCategory String? @map("industry_big_category") // 所属新证监会行业（大类行业）
  industryDetail     String? @map("industry_detail") // 所属新证监会行业（全部明细）
  updateTime         DateTime @map("update_time") // 表格更新时间
  
  // 反向关联：与基金行业配置表通过行业名称建立关联
  fundIndustryInfos  TonghuashunFundIndustryInfo[] @relation("StockIndustryFundRelation")

  // 添加索引
  @@index([securityCode]) // 证券代码索引
  @@index([securityName]) // 证券简称索引
  @@index([companyFullName]) // 公司全称索引
  @@index([industryCategory]) // 门类行业索引
  
  
  @@map("tonghuashun_stock_industry_info")
}
表二：基金行业配置表（可命名为tonghuashun_fund_industry_info）
该表记录基金在各行业的配置情况，需通过联合主键保证数据唯一性。
字段名
数据类型
说明
约束
fund_code
VARCHAR(20)
基金代码
联合主键，非空
industry_code
VARCHAR(20)
行业代码
联合主键，非空
fund_name
VARCHAR(100)
基金名称
非空
industry_name
VARCHAR(100)
行业名称
非空
market_value
DECIMAL(15,2)
市值 (万元)
可空
net_value_ratio
DECIMAL(10,4)
占净值比 (%)
可空
stock_invest_ratio
DECIMAL(10,4)
占股票投资市值比 (%)
可空
standard_allocation_ratio
DECIMAL(10,4)
股票市场标准行业配置比例 (%)
可空
relative_allocation_ratio
DECIMAL(10,4)
相对标准行业配置比例 (%)
可空
market_value_growth_rate
DECIMAL(10,4)
市值增长率 (%)
可空
investment_type
VARCHAR(50)
投资类型
可空
management_company
VARCHAR(100)
管理公司
可空
update_time
TIMESTAMP
表格更新时间
非空
示例：
[图片]
设计说明：
  - 采用fund_code（基金代码）和industry_code（行业代码）作为联合主键，因为同一基金对同一行业的配置数据是唯一的。
  - 数值型字段（如市值、各类比例）使用DECIMAL类型，保证精度，避免浮点数误差。
  - 保留industry_name字段，方便直观查看行业名称，同时可与表一的行业字段进行关联校验。
表结构设计
/**
 * 基金行业配置表
 * @description 记录基金在各行业的配置情况，通过基金代码和行业代码联合唯一标识
 */
model TonghuashunFundIndustryInfo {
  fundCode                String  @map("fund_code") // 基金代码
  industryCode            String  @map("industry_code") // 行业代码
  fundName                String  @map("fund_name") // 基金名称
  industryName            String  @map("industry_name") // 行业名称
  marketValue             Decimal? @map("market_value", precision: 15, scale: 2) // 市值 (万元)
  netValueRatio           Decimal? @map("net_value_ratio", precision: 10, scale: 4) // 占净值比 (%)
  stockInvestRatio        Decimal? @map("stock_invest_ratio", precision: 10, scale: 4) // 占股票投资市值比 (%)
  standardAllocationRatio Decimal? @map("standard_allocation_ratio", precision: 10, scale: 4) // 股票市场标准行业配置比例 (%)
  relativeAllocationRatio Decimal? @map("relative_allocation_ratio", precision: 10, scale: 4) // 相对标准行业配置比例 (%)
  marketValueGrowthRate   Decimal? @map("market_value_growth_rate", precision: 10, scale: 4) // 市值增长率 (%)
  investmentType          String? @map("investment_type") // 投资类型
  managementCompany       String? @map("management_company") // 管理公司
  updateTime              DateTime @map("update_time") // 表格更新时间
  
  // 联合主键
  @@id([fundCode, industryCode])
  // 与证券行业表的关联
  @@relation("StockIndustryFundRelation", fields: [industryName], references: [industryCategory])

  // 添加索引
  @@index([fundCode]) // 基金代码索引
  @@index([industryCode]) // 行业代码索引
  @@index([industryName]) // 行业名称索引
  @@index([managementCompany]) // 管理公司索引
  @@index([updateTime]) // 更新时间索引  // 添加索引
  @@index([fundCode]) // 基金代码索引
  @@index([industryCode]) // 行业代码索引
  @@index([industryName]) // 行业名称索引
  @@index([managementCompany]) // 管理公司索引
  @@index([updateTime]) // 更新时间索引

  @@map("tonghuashun_fund_industry_info")
}
表之间的关联设计
- 关联字段：表二的industry_name（行业名称）可与表一的 industry_category（所属新证监会行业）相关字段（如门类行业、大类行业等）进行关联，具体关联层级需根据业务需求确定（例如按门类行业关联）。
- 注意事项：若行业名称存在同名不同类的情况，建议以表二中的industry_code为基准，在数据库中新增一张industry表（存储行业代码、各层级行业名称的对应关系），再分别与表一、表二关联，确保行业信息的一致性。
设计流程图：
暂时无法在飞书文档外展示此内容