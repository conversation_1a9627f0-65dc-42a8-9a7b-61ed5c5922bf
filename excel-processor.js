const fs = require('fs');
const path = require('path');
const XLSX = require('xlsx');

class ExcelProcessor {
  constructor(filePath, batchSize = 1000) {
    this.filePath = filePath;
    this.batchSize = batchSize;
    this.currentBatch = [];
    this.batchNumber = 1;
    this.totalRows = 0;
    this.headers = [];
    
    // 确保data目录存在
    this.ensureDataDirectory();
  }

  ensureDataDirectory() {
    const dataDir = path.join(__dirname, 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
      console.log('✅ 创建data目录');
    }
  }

  // 解析前几行数据来识别字段结构
  async analyzeFileStructure(previewRows = 5) {
    console.log('🔍 正在分析文件结构...');
    
    try {
      const workbook = XLSX.readFile(this.filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      
      // 获取工作表范围
      const range = XLSX.utils.decode_range(worksheet['!ref']);
      console.log(`📊 工作表范围: ${worksheet['!ref']}`);
      
      // 提取表头
      this.headers = [];
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: range.s.r, c: col });
        const cell = worksheet[cellAddress];
        this.headers.push(cell ? cell.v : `Column_${col}`);
      }
      
      console.log('📋 识别到的字段名称:');
      this.headers.forEach((header, index) => {
        console.log(`  ${index + 1}. ${header}`);
      });
      
      // 预览前几行数据（格式化后的结构）
      console.log(`\n📖 预览前${previewRows}行数据（格式化为数据库表结构）:`);
      for (let row = range.s.r + 1; row <= Math.min(range.s.r + previewRows, range.e.r); row++) {
        const rowData = {};
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          const cell = worksheet[cellAddress];
          rowData[this.headers[col]] = cell ? cell.v : null;
        }
        const formattedData = this.formatToDbStructure(rowData);
        console.log(`  行${row}: `, JSON.stringify(formattedData, null, 2));
      }
      
      console.log(`\n📈 总行数: ${range.e.r - range.s.r} (不包括表头)`);
      return true;
      
    } catch (error) {
      console.error('❌ 文件分析失败:', error.message);
      return false;
    }
  }

  // 格式化数据为数据库表结构
  formatToDbStructure(rawData) {
    const currentTime = new Date().toISOString();

    return {
      security_code: rawData["证券代码"] || "",
      security_name: rawData["证券名称"] || "",
      company_full_name: rawData["公司中文名称"] || "",
      industry_category: rawData["所属新证监会行业\n[行业级别]  门类行业\n[截止日期]  最新"] || "",
      industry_big_category: rawData["所属新证监会行业\n[行业级别]  大类行业\n[截止日期]  最新"] || null,
      industry_detail: rawData["所属新证监会行业\n[行业级别]  全部明细\n[截止日期]  最新"] || null,
      update_time: currentTime
    };
  }

  // 流式处理完整文件
  async processFile() {
    console.log('\n🚀 开始流式处理文件...');

    try {
      const workbook = XLSX.readFile(this.filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const range = XLSX.utils.decode_range(worksheet['!ref']);

      // 处理数据行
      for (let row = range.s.r + 1; row <= range.e.r; row++) {
        const rowData = {};

        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          const cell = worksheet[cellAddress];
          rowData[this.headers[col]] = cell ? cell.v : null;
        }

        // 格式化为数据库表结构
        const formattedData = this.formatToDbStructure(rowData);
        this.currentBatch.push(formattedData);
        this.totalRows++;

        // 当批次达到指定大小时保存
        if (this.currentBatch.length >= this.batchSize) {
          await this.saveBatch();
        }

        // 显示进度
        if (this.totalRows % 500 === 0) {
          console.log(`📊 已处理 ${this.totalRows} 行数据...`);
        }
      }

      // 保存最后一批数据
      if (this.currentBatch.length > 0) {
        await this.saveBatch();
      }

      console.log(`\n✅ 处理完成！`);
      console.log(`📈 总共处理: ${this.totalRows} 行数据`);
      console.log(`📁 生成文件: ${this.batchNumber - 1} 个批次文件`);

      // 显示数据统计
      this.showDataStatistics();

    } catch (error) {
      console.error('❌ 文件处理失败:', error.message);
    }
  }

  async saveBatch() {
    const fileName = `tonghuashun_stock_batch_${String(this.batchNumber).padStart(3, '0')}.json`;
    const filePath = path.join(__dirname, 'data', fileName);

    const batchData = {
      tableName: "tonghuashun_stock_industry_info",
      batchNumber: this.batchNumber,
      recordCount: this.currentBatch.length,
      timestamp: new Date().toISOString(),
      description: "同花顺股票行业信息数据（已格式化为数据库表结构）",
      data: this.currentBatch
    };

    fs.writeFileSync(filePath, JSON.stringify(batchData, null, 2));
    console.log(`💾 保存批次 ${this.batchNumber}: ${this.currentBatch.length} 条记录 -> ${fileName}`);

    this.currentBatch = [];
    this.batchNumber++;
  }

  // 显示数据统计信息
  showDataStatistics() {
    console.log('\n📊 数据统计信息:');
    console.log(`📋 数据表名: tonghuashun_stock_industry_info`);
    console.log(`📈 总记录数: ${this.totalRows}`);
    console.log(`📁 批次文件数: ${this.batchNumber - 1}`);

    // 读取第一个批次文件来显示数据样例
    try {
      const firstBatchFile = path.join(__dirname, 'data', 'tonghuashun_stock_batch_001.json');
      if (fs.existsSync(firstBatchFile)) {
        const firstBatch = JSON.parse(fs.readFileSync(firstBatchFile, 'utf8'));
        if (firstBatch.data && firstBatch.data.length > 0) {
          console.log('\n📖 格式化后的数据样例:');
          console.log(JSON.stringify(firstBatch.data[0], null, 2));

          // 统计行业分布
          const industryStats = {};
          firstBatch.data.forEach(record => {
            const industry = record.industry_category;
            if (industry) {
              industryStats[industry] = (industryStats[industry] || 0) + 1;
            }
          });

          console.log('\n🏭 本批次门类行业分布:');
          Object.entries(industryStats)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .forEach(([industry, count]) => {
              console.log(`   ${industry}: ${count} 家公司`);
            });
        }
      }
    } catch (error) {
      console.log('⚠️ 无法读取统计信息:', error.message);
    }
  }
}

// 主函数
async function main(excelFile='全部A股（从股票到行业）.xlsx') {
  
  // 检查文件是否存在
  if (!fs.existsSync(excelFile)) {
    console.error(`❌ 文件不存在: ${excelFile}`);
    console.log('请确保文件在当前目录下');
    return;
  }
  
  const processor = new ExcelProcessor(excelFile, 1000);
  
  // 先分析文件结构
  const analyzed = await processor.analyzeFileStructure(3);
  
  if (analyzed) {
    console.log('\n❓ 字段结构是否正确？(y/n)');
    
    // 简单的用户确认（在实际使用中可以添加readline）
    console.log('继续处理完整文件...');
    await processor.processFile();
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = ExcelProcessor;