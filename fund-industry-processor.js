const fs = require('fs');
const path = require('path');
const XLSX = require('xlsx');

class FundIndustryProcessor {
  constructor(filePath, batchSize = 1000) {
    this.filePath = filePath;
    this.batchSize = batchSize;
    this.currentBatch = [];
    this.batchNumber = 1;
    this.totalRows = 0;
    this.headers = [];

    // 确保fund_industry_info目录存在
    this.ensureDataDirectory();
  }

  ensureDataDirectory() {
    const dataDir = path.join(__dirname, 'fund_industry_info');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
      console.log('✅ 创建fund_industry_info目录');
    }
  }

  // 分析基金行业配置表的文件结构
  async analyzeFileStructure() {
    console.log('🔍 正在分析基金行业配置表文件结构...');
    
    try {
      const workbook = XLSX.readFile(this.filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      
      console.log(`📊 工作表名称: ${sheetName}`);
      
      // 获取工作表范围
      const range = XLSX.utils.decode_range(worksheet['!ref']);
      console.log(`📊 工作表范围: ${worksheet['!ref']}`);
      console.log(`📈 总行数: ${range.e.r - range.s.r} (不包括表头)`);
      
      // 提取表头
      this.headers = [];
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: range.s.r, c: col });
        const cell = worksheet[cellAddress];
        this.headers.push(cell ? cell.v : `Column_${col}`);
      }
      
      console.log('\n📋 识别到的字段名称:');
      this.headers.forEach((header, index) => {
        console.log(`  ${index + 1}. ${header}`);
      });
      
      // 流水打印前10行原始数据
      console.log('\n📖 前10行原始数据流水日志:');
      console.log('=' .repeat(80));
      
      const maxRows = Math.min(10, range.e.r - range.s.r);
      for (let row = range.s.r + 1; row <= range.s.r + maxRows; row++) {
        console.log(`\n第${row - range.s.r}行数据:`);
        console.log('-'.repeat(40));
        
        const rowData = {};
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          const cell = worksheet[cellAddress];
          const value = cell ? cell.v : null;
          const fieldName = this.headers[col];
          
          rowData[fieldName] = value;
          console.log(`  ${fieldName}: ${value}`);
        }
        
        console.log(`  原始JSON: ${JSON.stringify(rowData, null, 2)}`);
      }
      
      console.log('\n' + '='.repeat(80));
      console.log('📝 请根据以上字段信息提供正确的字段映射关系');
      
      return true;
      
    } catch (error) {
      console.error('❌ 文件分析失败:', error.message);
      return false;
    }
  }

  // 格式化数据为数据库表结构 - tonghuashun_fund_industry_info
  formatToDbStructure(rawData) {
    const currentTime = new Date().toISOString();

    // 处理市值增长率的特殊值
    const parseGrowthRate = (value) => {
      if (value === '--' || value === null || value === undefined) {
        return null;
      }
      return parseFloat(value);
    };

    // 处理标准配置比例的特殊值
    const parseRatio = (value) => {
      if (value === '--' || value === null || value === undefined) {
        return null;
      }
      return parseFloat(value);
    };

    return {
      fund_code: rawData["Column_0"] || "",
      industry_code: rawData["Column_2"] || "",
      fund_name: rawData["Column_1"] || "",
      industry_name: rawData["Column_3"] || "",
      market_value: rawData["2025年中报"] ? parseFloat(rawData["2025年中报"]) : null,
      net_value_ratio: rawData["Column_5"] ? parseFloat(rawData["Column_5"]) : null,
      stock_invest_ratio: rawData["Column_6"] ? parseFloat(rawData["Column_6"]) : null,
      standard_allocation_ratio: parseRatio(rawData["Column_7"]),
      relative_allocation_ratio: parseRatio(rawData["Column_8"]),
      market_value_growth_rate: parseGrowthRate(rawData["Column_9"]),
      investment_type: rawData["Column_10"] || null,
      management_company: rawData["Column_11"] || null,
      update_time: currentTime
    };
  }

  // 流式处理完整文件
  async processFile() {
    console.log('\n🚀 开始流式处理基金行业配置文件...');

    try {
      const workbook = XLSX.readFile(this.filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const range = XLSX.utils.decode_range(worksheet['!ref']);

      // 跳过第一行（表头行），从第二行开始处理数据
      for (let row = range.s.r + 1; row <= range.e.r; row++) {
        const rowData = {};

        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          const cell = worksheet[cellAddress];
          rowData[this.headers[col]] = cell ? cell.v : null;
        }

        // 格式化为数据库表结构
        const formattedData = this.formatToDbStructure(rowData);
        this.currentBatch.push(formattedData);
        this.totalRows++;

        // 当批次达到指定大小时保存
        if (this.currentBatch.length >= this.batchSize) {
          await this.saveBatch();
        }

        // 显示进度
        if (this.totalRows % 1000 === 0) {
          console.log(`📊 已处理 ${this.totalRows} 行数据...`);
        }
      }

      // 保存最后一批数据
      if (this.currentBatch.length > 0) {
        await this.saveBatch();
      }

      console.log(`\n✅ 处理完成！`);
      console.log(`📈 总共处理: ${this.totalRows} 行数据`);
      console.log(`📁 生成文件: ${this.batchNumber - 1} 个批次文件`);

      // 显示数据统计
      this.showDataStatistics();

    } catch (error) {
      console.error('❌ 文件处理失败:', error.message);
    }
  }

  async saveBatch() {
    const fileName = `tonghuashun_fund_industry_batch_${String(this.batchNumber).padStart(3, '0')}.json`;
    const filePath = path.join(__dirname, 'fund_industry_info', fileName);

    const batchData = {
      tableName: "tonghuashun_fund_industry_info",
      batchNumber: this.batchNumber,
      recordCount: this.currentBatch.length,
      timestamp: new Date().toISOString(),
      description: "同花顺基金行业配置数据（已格式化为数据库表结构）",
      data: this.currentBatch
    };

    fs.writeFileSync(filePath, JSON.stringify(batchData, null, 2));
    console.log(`💾 保存批次 ${this.batchNumber}: ${this.currentBatch.length} 条记录 -> ${fileName}`);

    this.currentBatch = [];
    this.batchNumber++;
  }

  // 显示数据统计信息
  showDataStatistics() {
    console.log('\n📊 数据统计信息:');
    console.log(`📋 数据表名: tonghuashun_fund_industry_info`);
    console.log(`📈 总记录数: ${this.totalRows}`);
    console.log(`📁 批次文件数: ${this.batchNumber - 1}`);

    // 读取第一个批次文件来显示数据样例
    try {
      const firstBatchFile = path.join(__dirname, 'fund_industry_info', 'tonghuashun_fund_industry_batch_001.json');
      if (fs.existsSync(firstBatchFile)) {
        const firstBatch = JSON.parse(fs.readFileSync(firstBatchFile, 'utf8'));
        if (firstBatch.data && firstBatch.data.length > 0) {
          console.log('\n📖 格式化后的数据样例:');
          console.log(JSON.stringify(firstBatch.data[0], null, 2));

          // 统计基金类型分布
          const fundTypeStats = {};
          const industryStats = {};
          firstBatch.data.forEach(record => {
            const fundType = record.investment_type;
            const industry = record.industry_name;

            if (fundType) {
              fundTypeStats[fundType] = (fundTypeStats[fundType] || 0) + 1;
            }
            if (industry) {
              industryStats[industry] = (industryStats[industry] || 0) + 1;
            }
          });

          console.log('\n📊 本批次基金类型分布:');
          Object.entries(fundTypeStats)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .forEach(([type, count]) => {
              console.log(`   ${type}: ${count} 条记录`);
            });

          console.log('\n🏭 本批次行业分布:');
          Object.entries(industryStats)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .forEach(([industry, count]) => {
              console.log(`   ${industry}: ${count} 条记录`);
            });
        }
      }
    } catch (error) {
      console.log('⚠️ 无法读取统计信息:', error.message);
    }
  }
}

// 主函数
async function main() {
  const excelFile = '基金行业配置表.xlsx';
  
  // 检查文件是否存在
  if (!fs.existsSync(excelFile)) {
    console.error(`❌ 文件不存在: ${excelFile}`);
    console.log('请确保文件在当前目录下');
    return;
  }
  
  console.log(`🚀 开始分析基金行业配置表: ${excelFile}`);

  const processor = new FundIndustryProcessor(excelFile, 1000);

  // 先分析文件结构并打印前10行数据
  const analyzed = await processor.analyzeFileStructure();

  if (analyzed) {
    console.log('\n❓ 字段映射是否正确？继续处理完整文件...');
    await processor.processFile();
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = FundIndustryProcessor;
